<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/main_bg"
        android:gravity="center_vertical"
        android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_bytime_other"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/main_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <Button
            android:id="@+id/button_setup_alarm_jni"
            android:layout_width="0dp"
            android:layout_weight="4"
            android:layout_height="50dp"
            android:textSize="15sp"
            android:textAllCaps="false"
            android:text="Alarm_jni"/>

        <Space
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1" />
        <Button
            android:id="@+id/button_setup_alarm_jna"
            android:layout_width="0dp"
            android:layout_weight="4"
            android:layout_height="50dp"
            android:textSize="15sp"
            android:textAllCaps="false"
            android:text="Alarm_jna"/>

        <Space
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/button_del_alarm"
            android:layout_width="0dp"
            android:layout_weight="4"
            android:layout_height="50dp"
            android:textSize="15sp"
            android:textAllCaps="false"
            android:text="DelAlarm"/>

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="-146dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="alarm_time" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="alarm_ip" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="alarm_command" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:baselineAligned="true"
            android:orientation="horizontal">

            <ListView
                android:id="@+id/list_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"></ListView>
        </LinearLayout>
    </LinearLayout>
    </LinearLayout>


</android.support.constraint.ConstraintLayout>