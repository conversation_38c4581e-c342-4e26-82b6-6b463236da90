<fragconfig>
    <!--<fragitem-->
        <!--className="com.hik.netsdk.SimpleDemo.View.BusinessUI.Fragment.FragTest1"-->
        <!--titile="FragTest1"-->
        <!--args="@null"/>-->
    <!--<fragitem-->
        <!--className="com.hik.netsdk.SimpleDemo.View.BusinessUI.Fragment.FragTest2.FragTest2"-->
        <!--titile="FragTest2"-->
        <!--args="@null"/>-->
    <fragitem
        className="com.hik.netsdk.SimpleDemo.View.BusinessUI.Fragment.Preview.FragPreview"
        titile="Preview"
        args="@null"/>
    <fragitem
        className="com.hik.netsdk.SimpleDemo.View.BusinessUI.Fragment.PlayBack.FragPlayBack"
        titile="PlayBack"
        args="@null"/>
    <fragitem
        className="com.hik.netsdk.SimpleDemo.View.BusinessUI.Fragment.Alarm.FragAlarm"
        titile="Alarm"
        args="@null"/>
    <fragitem
        className="com.hik.netsdk.SimpleDemo.View.BusinessUI.Fragment.Transport.FragTransport"
        titile="Transport"
        args="@null"/>
    <fragitem
        className="com.hik.netsdk.SimpleDemo.View.BusinessUI.Fragment.Configure.FragConfig"
        titile="Config"
        args="@null"/>
    <fragitem
        className="com.hik.netsdk.SimpleDemo.View.BusinessUI.Fragment.PassThrough.FragPassThrough"
        titile="PassThrough"
        args="@null"/>
    <!--<fragitem-->
        <!--className="com.hik.netsdk.SimpleDemo.View.BusinessUI.Fragment.FragTest2.FragTest2"-->
        <!--titile="TextureView"-->
        <!--args="@null"/>-->
</fragconfig>
