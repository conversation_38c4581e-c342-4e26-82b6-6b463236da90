<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal" >

    <TextView
        android:id="@+id/fileIndex"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:textSize="15sp"
        android:text="@string/File_index"
        android:textAppearance="?android:attr/textAppearanceMedium" />

    <Space
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/fileName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="20dp"
        android:textSize="15sp"
        android:text="@string/File_name"
        android:textAppearance="?android:attr/textAppearanceMedium" />
</LinearLayout>
