# Camera SDK Demo

这是一个基于海康威视HCNetSDK的简化Android摄像头预览Demo项目。

## 功能特性

- 设备登录/登出
- 获取摄像头列表
- 选择摄像头进行预览
- 支持主码流/子码流切换
- 抓拍功能

## 项目结构

```
CameraSDKDemo/
├── app/
│   ├── src/main/java/com/hik/camerasdk/
│   │   ├── MainActivity.java          # 主Activity
│   │   ├── SDKManager.java           # SDK管理类
│   │   ├── DeviceManager.java        # 设备管理类
│   │   ├── PreviewManager.java       # 预览管理类
│   │   ├── DeviceInfo.java           # 设备信息类
│   │   └── CameraInfo.java           # 摄像头信息类
│   ├── libs/                         # SDK库文件
│   │   ├── HCNetSDK.jar
│   │   ├── jna.jar
│   │   └── PlayerSDK_hcnetsdk.jar
│   └── src/main/res/                 # 资源文件
└── README.md
```

## 使用说明

### 1. 环境要求

- Android Studio 3.6+
- Android SDK API Level 19+
- 海康威视网络摄像头设备

### 2. 配置设备

1. 确保Android设备与摄像头在同一网络
2. 获取摄像头的IP地址、端口、用户名和密码

### 3. 运行步骤

1. 打开应用
2. 在登录区域输入设备信息：
   - IP地址：摄像头的IP地址
   - 端口：通常为8000
   - 用户名：通常为admin
   - 密码：设备密码
3. 点击"登录"按钮
4. 登录成功后，左侧会显示摄像头列表
5. 选择要预览的摄像头
6. 选择码流类型（主码流/子码流）
7. 点击"开始预览"按钮
8. 可以使用"抓拍"功能保存图片

### 4. 主要类说明

#### SDKManager
- 负责SDK的初始化和清理
- 单例模式，确保SDK只初始化一次

#### DeviceManager
- 管理设备的登录和登出
- 维护设备列表和当前设备状态

#### PreviewManager
- 管理摄像头预览功能
- 支持SurfaceView和TextureView两种预览方式
- 提供抓拍功能

#### DeviceInfo
- 封装设备信息（IP、端口、用户名、密码等）
- 维护设备在线状态和用户ID

#### CameraInfo
- 封装摄像头信息（通道号、名称、码流类型等）
- 支持主码流和子码流切换

### 5. 权限说明

应用需要以下权限：
- INTERNET：网络访问
- ACCESS_NETWORK_STATE：网络状态
- ACCESS_WIFI_STATE：WiFi状态
- WRITE_EXTERNAL_STORAGE：存储写入（抓拍功能）
- READ_EXTERNAL_STORAGE：存储读取
- CAMERA：摄像头访问
- RECORD_AUDIO：录音（如需音频功能）

### 6. 注意事项

1. 确保设备网络连接正常
2. 摄像头设备需要支持RTSP协议
3. 首次运行需要授予存储权限
4. 预览时建议使用横屏模式
5. 抓拍的图片保存在/mnt/sdcard/目录下

### 7. 常见问题

**Q: 登录失败怎么办？**
A: 检查IP地址、端口、用户名密码是否正确，确保网络连通性。

**Q: 预览黑屏怎么办？**
A: 检查摄像头是否在线，尝试切换码流类型。

**Q: 抓拍失败怎么办？**
A: 确保已授予存储权限，检查存储空间是否充足。

## 开发说明

这个Demo基于原始的ASSimpleDemo项目简化而来，保留了核心的SDK使用功能，去除了复杂的UI和不必要的功能，更适合学习和二次开发。

如需扩展功能，可以参考原始项目中的其他模块实现。
