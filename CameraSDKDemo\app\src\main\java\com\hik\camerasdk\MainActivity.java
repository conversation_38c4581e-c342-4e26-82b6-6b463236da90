package com.hik.camerasdk;

import android.app.Activity;
import android.os.Bundle;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Spinner;
import android.widget.Toast;
import java.util.ArrayList;
import java.util.List;

/**
 * 主Activity
 * 包含设备登录、摄像头列表和预览功能
 */
public class MainActivity extends Activity implements SurfaceHolder.Callback {
    
    // UI控件
    private EditText etIpAddress;
    private EditText etPort;
    private EditText etUsername;
    private EditText etPassword;
    private Button btnLogin;
    private Button btnLogout;
    private ListView lvCameraList;
    private SurfaceView svPreview;
    private Button btnStartPreview;
    private Button btnStopPreview;
    private Button btnCapture;
    private Spinner spStreamType;
    
    // 数据
    private DeviceInfo currentDevice;
    private List<CameraInfo> cameraList;
    private CameraInfo selectedCamera;
    private ArrayAdapter<CameraInfo> cameraAdapter;
    private SurfaceHolder surfaceHolder;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // 初始化SDK
        SDKManager.getInstance();
        
        initViews();
        initData();
        setupListeners();
    }
    
    /**
     * 初始化视图
     */
    private void initViews() {
        // 登录相关
        etIpAddress = findViewById(R.id.et_ip_address);
        etPort = findViewById(R.id.et_port);
        etUsername = findViewById(R.id.et_username);
        etPassword = findViewById(R.id.et_password);
        btnLogin = findViewById(R.id.btn_login);
        btnLogout = findViewById(R.id.btn_logout);
        
        // 摄像头列表
        lvCameraList = findViewById(R.id.lv_camera_list);
        
        // 预览相关
        svPreview = findViewById(R.id.sv_preview);
        btnStartPreview = findViewById(R.id.btn_start_preview);
        btnStopPreview = findViewById(R.id.btn_stop_preview);
        btnCapture = findViewById(R.id.btn_capture);
        spStreamType = findViewById(R.id.sp_stream_type);
        
        // 设置SurfaceView
        surfaceHolder = svPreview.getHolder();
        surfaceHolder.addCallback(this);
        
        // 设置默认值
        etIpAddress.setText("************");
        etPort.setText("8000");
        etUsername.setText("admin");
        etPassword.setText("12345");
    }
    
    /**
     * 初始化数据
     */
    private void initData() {
        cameraList = new ArrayList<>();
        cameraAdapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_1, cameraList);
        lvCameraList.setAdapter(cameraAdapter);
        
        // 初始化码流类型选择器
        String[] streamTypes = {"主码流", "子码流"};
        ArrayAdapter<String> streamAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, streamTypes);
        streamAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spStreamType.setAdapter(streamAdapter);
        
        updateUIState(false);
    }
    
    /**
     * 设置监听器
     */
    private void setupListeners() {
        // 登录按钮
        btnLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                loginDevice();
            }
        });
        
        // 登出按钮
        btnLogout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                logoutDevice();
            }
        });
        
        // 摄像头列表选择
        lvCameraList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                selectedCamera = cameraList.get(position);
                // 更新选中摄像头的码流类型
                selectedCamera.setStreamType(spStreamType.getSelectedItemPosition());
                Toast.makeText(MainActivity.this, "选中: " + selectedCamera.toString(), Toast.LENGTH_SHORT).show();
            }
        });
        
        // 码流类型选择
        spStreamType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (selectedCamera != null) {
                    selectedCamera.setStreamType(position);
                }
            }
            
            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });
        
        // 开始预览
        btnStartPreview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startPreview();
            }
        });
        
        // 停止预览
        btnStopPreview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                stopPreview();
            }
        });
        
        // 抓拍
        btnCapture.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                capturePicture();
            }
        });
    }
    
    /**
     * 登录设备
     */
    private void loginDevice() {
        String ip = etIpAddress.getText().toString().trim();
        String portStr = etPort.getText().toString().trim();
        String username = etUsername.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        
        if (ip.isEmpty() || portStr.isEmpty() || username.isEmpty() || password.isEmpty()) {
            Toast.makeText(this, "请填写完整的登录信息", Toast.LENGTH_SHORT).show();
            return;
        }
        
        try {
            int port = Integer.parseInt(portStr);
            currentDevice = new DeviceInfo("摄像头设备", ip, port, username, password);
            
            boolean success = DeviceManager.getInstance().loginDevice(currentDevice);
            if (success) {
                Toast.makeText(this, "登录成功", Toast.LENGTH_SHORT).show();
                updateUIState(true);
                loadCameraList();
            } else {
                Toast.makeText(this, "登录失败", Toast.LENGTH_SHORT).show();
            }
        } catch (NumberFormatException e) {
            Toast.makeText(this, "端口号格式错误", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 登出设备
     */
    private void logoutDevice() {
        if (currentDevice != null) {
            // 先停止预览
            stopPreview();
            
            boolean success = DeviceManager.getInstance().logoutDevice(currentDevice);
            if (success) {
                Toast.makeText(this, "登出成功", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "登出失败", Toast.LENGTH_SHORT).show();
            }
            
            currentDevice = null;
            updateUIState(false);
            cameraList.clear();
            cameraAdapter.notifyDataSetChanged();
        }
    }
    
    /**
     * 加载摄像头列表
     */
    private void loadCameraList() {
        if (currentDevice != null && currentDevice.isOnline()) {
            List<CameraInfo> cameras = PreviewManager.getInstance().getCameraList(currentDevice);
            cameraList.clear();
            cameraList.addAll(cameras);
            cameraAdapter.notifyDataSetChanged();
        }
    }
    
    /**
     * 开始预览
     */
    private void startPreview() {
        if (selectedCamera == null) {
            Toast.makeText(this, "请先选择摄像头", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (currentDevice == null || !currentDevice.isOnline()) {
            Toast.makeText(this, "设备未在线", Toast.LENGTH_SHORT).show();
            return;
        }
        
        boolean success = PreviewManager.getInstance().startPreview(currentDevice, selectedCamera, surfaceHolder);
        if (success) {
            Toast.makeText(this, "预览开始", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(this, "预览失败", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 停止预览
     */
    private void stopPreview() {
        boolean success = PreviewManager.getInstance().stopPreview();
        if (success) {
            Toast.makeText(this, "预览停止", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 抓拍图片
     */
    private void capturePicture() {
        if (!PreviewManager.getInstance().isPreviewActive()) {
            Toast.makeText(this, "请先开始预览", Toast.LENGTH_SHORT).show();
            return;
        }
        
        String filePath = "/mnt/sdcard/capture_" + System.currentTimeMillis() + ".jpg";
        boolean success = PreviewManager.getInstance().capturePicture(filePath);
        if (success) {
            Toast.makeText(this, "抓拍成功: " + filePath, Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(this, "抓拍失败", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 更新UI状态
     */
    private void updateUIState(boolean isLoggedIn) {
        btnLogin.setEnabled(!isLoggedIn);
        btnLogout.setEnabled(isLoggedIn);
        etIpAddress.setEnabled(!isLoggedIn);
        etPort.setEnabled(!isLoggedIn);
        etUsername.setEnabled(!isLoggedIn);
        etPassword.setEnabled(!isLoggedIn);
    }
    
    // SurfaceHolder.Callback 实现
    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        // Surface创建时的处理
    }
    
    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        // Surface改变时的处理
    }
    
    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        // Surface销毁时停止预览
        stopPreview();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理资源
        PreviewManager.getInstance().cleanup();
        DeviceManager.getInstance().cleanup();
        SDKManager.getInstance().cleanup();
    }
}
