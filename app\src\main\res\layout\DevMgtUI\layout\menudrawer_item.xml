<?xml version="1.0" encoding="utf-8"?>
<!--<TextView xmlns:android="http://schemas.android.com/apk/res/android"-->
<!--android:layout_width="match_parent"-->
<!--android:layout_height="wrap_content" >-->
<!--</TextView>-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:descendantFocusability="blocksDescendants"
    tools:context=".View.MainActivity">
    <ImageView
        android:id="@+id/image"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="5dp"/>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="0dp"
        android:layout_weight="3">
        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="0dp"
            android:layout_marginTop="0dp"
            android:textColor="#000000"
            android:textSize="20dp"/>
        <TextView
            android:id="@+id/ip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="0dp"
            android:layout_marginLeft="0dp"
            android:textColor="#000000"
            android:textSize="10dp"/>
    </LinearLayout>
    <!--<Button-->
        <!--android:layout_weight="1"-->
        <!--android:id="@+id/button"-->
        <!--android:layout_marginTop="12dp"-->
        <!--android:layout_width="20dp"-->
        <!--android:layout_height="40dp"-->
        <!--android:text="下载"-->
        <!--android:textSize="10dp"/>-->
    <View
        android:id="@+id/nav_view"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="start"
        android:fitsSystemWindows="true" />
</LinearLayout>