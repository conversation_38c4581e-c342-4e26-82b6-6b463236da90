<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 设备登录区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#f0f0f0"
        android:padding="12dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设备登录"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="IP地址:" />

            <EditText
                android:id="@+id/et_ip_address"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="************" />

            <TextView
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:text="端口:" />

            <EditText
                android:id="@+id/et_port"
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:hint="8000"
                android:inputType="number" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <TextView
                android:layout_width="80dp"
                android:layout_height="wrap_content"
                android:text="用户名:" />

            <EditText
                android:id="@+id/et_username"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="admin" />

            <TextView
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:text="密码:" />

            <EditText
                android:id="@+id/et_password"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:hint="password"
                android:inputType="textPassword" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <Button
                android:id="@+id/btn_login"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="登录"
                android:layout_marginRight="8dp" />

            <Button
                android:id="@+id/btn_logout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="登出" />

        </LinearLayout>

    </LinearLayout>

    <!-- 摄像头列表和预览控制区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <!-- 左侧摄像头列表 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginRight="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="摄像头列表"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <ListView
                android:id="@+id/lv_camera_list"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="#f8f8f8" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="码流:"
                    android:layout_gravity="center_vertical" />

                <Spinner
                    android:id="@+id/sp_stream_type"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginLeft="8dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 右侧预览区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="视频预览"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <SurfaceView
                android:id="@+id/sv_preview"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="#000000" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <Button
                    android:id="@+id/btn_start_preview"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="开始预览"
                    android:layout_marginRight="4dp" />

                <Button
                    android:id="@+id/btn_stop_preview"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="停止预览"
                    android:layout_marginLeft="4dp"
                    android:layout_marginRight="4dp" />

                <Button
                    android:id="@+id/btn_capture"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="抓拍"
                    android:layout_marginLeft="4dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
