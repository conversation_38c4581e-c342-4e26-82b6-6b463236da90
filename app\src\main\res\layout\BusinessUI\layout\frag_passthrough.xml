<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin"
        tools:layout_editor_absoluteX="3dp"
        tools:layout_editor_absoluteY="14dp">

        <LinearLayout
            android:id="@+id/ll_serial_port_type"
            android:layout_width="375dp"
            android:layout_height="41dp"
            android:layout_marginEnd="52dp"
            android:layout_marginRight="52dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            tools:layout_editor_absoluteY="23dp">

            <TextView
                android:id="@+id/labal_serial_port_type"
                android:layout_width="141dp"
                android:layout_height="39dp"
                android:gravity="center_vertical"
                android:text="SerialPortType"
                android:textSize="15sp" />
            <RadioGroup
                android:id="@+id/radio_group_port_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="15dp">

                <RadioButton
                    android:id="@+id/radio_type_232"
                    android:layout_width="80dp"
                    android:layout_height="wrap_content"
                    android:text="232"
                    android:textSize="15sp" />

                <RadioButton
                    android:id="@+id/radio_type_485"
                    android:layout_width="78dp"
                    android:layout_height="wrap_content"
                    android:text="485"
                    android:textSize="15sp" />

            </RadioGroup>
        </LinearLayout>
        <View
            android:id="@+id/divider1"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/listDivider" />

        <LinearLayout
            android:id="@+id/ll_channel"
            android:layout_width="377dp"
            android:layout_height="41dp"
            android:layout_marginEnd="52dp"
            android:layout_marginRight="52dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            tools:layout_editor_absoluteY="15dp">

            <TextView
                android:id="@+id/label_channel"
                android:layout_width="141dp"
                android:layout_height="39dp"
                android:gravity="center_vertical"
                android:text="Channel"
                android:textSize="15sp" />

            <Spinner
                android:id="@+id/spinner_channel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingLeft="15dp"></Spinner>

        </LinearLayout>
        <View
            android:id="@+id/divider2"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/listDivider" />

        <LinearLayout
            android:id="@+id/ll_open_close_channel"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Button
                android:id="@+id/button_open_channel"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginLeft="20dp"
                android:layout_weight="2"
                android:text="OpenChannel"
                android:textAllCaps="false"
                android:textSize="15sp" />

            <Space
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_weight="1" />

            <Button
                android:id="@+id/button_close_channel"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginRight="20dp"
                android:layout_weight="2"
                android:text="CloseChannel"
                android:textAllCaps="false"
                android:textSize="15sp" />

        </LinearLayout>
        <View
            android:id="@+id/divider3"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/listDivider" />

    <LinearLayout
        android:id="@+id/ll_send_message"
        android:layout_width="wrap_content"
        android:layout_height="100dp"
        android:gravity="center_vertical"
        android:orientation="vertical"
        tools:layout_editor_absoluteY="26dp">

        <LinearLayout
            android:id="@+id/ll_send_interface"
            android:layout_width="348dp"
            android:layout_height="41dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="52dp"
            android:layout_marginRight="52dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            tools:layout_editor_absoluteY="15dp">

            <TextView
                android:id="@+id/label_send_interface"
                android:layout_width="100dp"
                android:layout_height="39dp"
                android:gravity="center_vertical"
                android:text="Interface"
                android:textSize="15sp" />

            <Spinner
                android:id="@+id/spinner_send_interface"
                android:layout_width="237dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:entries="@array/SendInterface"
                android:gravity="center_vertical"
                android:textSize="15sp"></Spinner>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_send_message_content"
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_margin = "5dp"
            android:layout_marginBottom="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">


            <EditText
                android:id="@+id/textinput_send_message"
                android:layout_width="246dp"
                android:layout_height="wrap_content"
                android:background="@drawable/multi_text_input_background"
                android:ems="10"
                android:gravity="start|top"
                android:inputType="textMultiLine"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:textSize="12dp"
                android:textColor="#000"
                android:minHeight="50dp"
                android:maxLines="20"
                android:scrollbars="vertical"
                android:padding="5dp" />

            <Button
                android:id="@+id/button_send_message"
                android:layout_width="68dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_weight="1"
                android:text="Send" />
        </LinearLayout>
    </LinearLayout>
        <LinearLayout
            android:id="@+id/ll_recv_data"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:background="@color/main_bg"
            android:orientation="vertical">

            <View
                android:id="@+id/divider4"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="?android:attr/listDivider" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="Time" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="Content" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="Hex" />

            </LinearLayout>
            <View
                android:id="@+id/divider5"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:background="?android:attr/listDivider" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:baselineAligned="true"
                android:orientation="horizontal"
                android:minHeight="250dp">
                <ListView
                    android:id="@+id/listview_recvinfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                </ListView>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>


</android.support.constraint.ConstraintLayout>