<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.hik.netsdk.SimpleDemo">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".View.MainActivity"
            android:label="@string/app_name"
            android:theme="@style/AppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".View.BusinessUI.Fragment.FragTest2.FragTest2Activity" />
        <activity android:name=".View.DevMgtUI.AddDevActivity" />
        <activity android:name=".View.DevMgtUI.DevInfoActivity" />
        <activity android:name=".View.BusinessUI.Fragment.Preview.FragPreviewBySurfaceView" />
        <activity android:name=".View.BusinessUI.Fragment.Preview.FragPreviewByTextureView" />
        <activity android:name=".View.BusinessUI.Fragment.PlayBack.FragPlayBackByTime" />
        <activity android:name=".View.BusinessUI.Fragment.PlayBack.FragPlayBackByFile" />
        <activity android:name=".View.BusinessUI.Fragment.PlayBack.FragPlayBackTheSelectFile"></activity>
    </application>

</manifest>