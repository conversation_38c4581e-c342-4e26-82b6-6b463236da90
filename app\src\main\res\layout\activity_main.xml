<?xml version="1.0" encoding="utf-8"?>
<android.support.v4.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:openDrawer="start">

    <include
        layout="@layout/app_bar_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/image_title"
            android:layout_width="240dp"
            android:layout_height="100dp"
            android:layout_weight="1"
            android:background="#ffeeeeee"
            android:scaleType="center"
            android:src="@color/design_default_color_primary" />
        <ListView android:id="@+id/left_drawer"
            android:layout_weight="4"
            android:layout_width="240dp"
            android:layout_height="wrap_content"
            android:choiceMode="singleChoice"
            android:divider="#33333333"
            android:dividerHeight="0dp"
            android:background="#ffeeeeee"
            android:listSelector="#B0B0B0">
        </ListView>
    </LinearLayout>


</android.support.v4.widget.DrawerLayout>
