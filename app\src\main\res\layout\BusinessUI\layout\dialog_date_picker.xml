<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/date_picker_bg"
        android:orientation="horizontal"
        android:padding="10dp">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="10dp"
            android:text="@string/cancel"
            android:textColor="@color/date_picker_text_light"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/title"
            android:textColor="@color/date_picker_text_light"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="10dp"
            android:text="@string/confirm"
            android:textColor="@color/date_picker_text_light"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/date_picker_divider" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/date_picker_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="20dp"
        android:paddingEnd="20dp"
        android:paddingStart="20dp"
        android:paddingTop="15dp">

        <com.hik.netsdk.SimpleDemo.View.BusinessUI.datepicker.PickerView
            android:id="@+id/dpv_year"
            android:layout_width="0dp"
            android:layout_height="160dp"
            android:layout_weight="3" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/year"
            android:textColor="@color/date_picker_text_dark"
            android:textSize="18sp" />

        <com.hik.netsdk.SimpleDemo.View.BusinessUI.datepicker.PickerView
            android:id="@+id/dpv_month"
            android:layout_width="0dp"
            android:layout_height="160dp"
            android:layout_weight="2" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/month"
            android:textColor="@color/date_picker_text_dark"
            android:textSize="18sp" />

        <com.hik.netsdk.SimpleDemo.View.BusinessUI.datepicker.PickerView
            android:id="@+id/dpv_day"
            android:layout_width="0dp"
            android:layout_height="160dp"
            android:layout_weight="2" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/day"
            android:textColor="@color/date_picker_text_dark"
            android:textSize="18sp" />

        <com.hik.netsdk.SimpleDemo.View.BusinessUI.datepicker.PickerView
            android:id="@+id/dpv_hour"
            android:layout_width="0dp"
            android:layout_height="160dp"
            android:layout_weight="2" />

        <TextView
            android:id="@+id/tv_hour_unit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/hour"
            android:textColor="@color/date_picker_text_dark"
            android:textSize="18sp" />

        <com.hik.netsdk.SimpleDemo.View.BusinessUI.datepicker.PickerView
            android:id="@+id/dpv_minute"
            android:layout_width="0dp"
            android:layout_height="160dp"
            android:layout_weight="2" />

        <TextView
            android:id="@+id/tv_minute_unit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/minute"
            android:textColor="@color/date_picker_text_dark"
            android:textSize="18sp" />

        <com.hik.netsdk.SimpleDemo.View.BusinessUI.datepicker.PickerView
            android:id="@+id/dpv_second"
            android:layout_width="0dp"
            android:layout_height="160dp"
            android:layout_weight="2" />

        <TextView
            android:id="@+id/tv_Second_unit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/second"
            android:textColor="@color/date_picker_text_dark"
            android:textSize="18sp" />

    </LinearLayout>

</LinearLayout>