package com.hik.camerasdk;

/**
 * 摄像头信息类
 */
public class CameraInfo {
    private int channelNumber;
    private String channelName;
    private int streamType; // 0-主码流, 1-子码流
    private boolean isOnline;
    
    public CameraInfo() {
    }
    
    public CameraInfo(int channelNumber, String channelName) {
        this.channelNumber = channelNumber;
        this.channelName = channelName;
        this.streamType = 0; // 默认主码流
        this.isOnline = true;
    }
    
    public int getChannelNumber() {
        return channelNumber;
    }
    
    public void setChannelNumber(int channelNumber) {
        this.channelNumber = channelNumber;
    }
    
    public String getChannelName() {
        return channelName;
    }
    
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
    
    public int getStreamType() {
        return streamType;
    }
    
    public void setStreamType(int streamType) {
        this.streamType = streamType;
    }
    
    public boolean isOnline() {
        return isOnline;
    }
    
    public void setOnline(boolean online) {
        isOnline = online;
    }
    
    @Override
    public String toString() {
        return "通道" + channelNumber + ": " + channelName + 
               " (" + (streamType == 0 ? "主码流" : "子码流") + ")";
    }
}
