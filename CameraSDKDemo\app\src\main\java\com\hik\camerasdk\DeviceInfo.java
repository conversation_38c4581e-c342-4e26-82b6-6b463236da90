package com.hik.camerasdk;

/**
 * 设备信息类
 */
public class DeviceInfo {
    private String deviceName;
    private String ipAddress;
    private int port;
    private String username;
    private String password;
    private int userId = -1;  // 登录后的用户ID
    private boolean isOnline = false;
    
    public DeviceInfo() {
    }
    
    public DeviceInfo(String deviceName, String ipAddress, int port, String username, String password) {
        this.deviceName = deviceName;
        this.ipAddress = ipAddress;
        this.port = port;
        this.username = username;
        this.password = password;
    }
    
    // Getters and Setters
    public String getDeviceName() {
        return deviceName;
    }
    
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public int getUserId() {
        return userId;
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
    }
    
    public boolean isOnline() {
        return isOnline;
    }
    
    public void setOnline(boolean online) {
        isOnline = online;
    }
    
    @Override
    public String toString() {
        return deviceName + " (" + ipAddress + ":" + port + ")";
    }
}
