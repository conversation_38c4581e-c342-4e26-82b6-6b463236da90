package com.hik.camerasdk;

import android.util.Log;
import com.hcnetsdk.jna.HCNetSDKJNAInstance;

/**
 * 简化的SDK管理类
 * 负责SDK的初始化和清理
 */
public class SDKManager {
    private static final String TAG = "SDKManager";
    private static SDKManager instance;
    
    private SDKManager() {
        initSDK();
    }
    
    public static synchronized SDKManager getInstance() {
        if (instance == null) {
            instance = new SDKManager();
        }
        return instance;
    }
    
    /**
     * 初始化SDK
     */
    private boolean initSDK() {
        try {
            if (!HCNetSDKJNAInstance.getInstance().NET_DVR_Init()) {
                Log.e(TAG, "SDK初始化失败!");
                return false;
            }
            
            // 设置日志文件路径
            HCNetSDKJNAInstance.getInstance().NET_DVR_SetLogToFile(3, "/mnt/sdcard/sdklog/", true);
            Log.i(TAG, "SDK初始化成功!");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "SDK初始化异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 清理SDK资源
     */
    public void cleanup() {
        try {
            if (!HCNetSDKJNAInstance.getInstance().NET_DVR_Cleanup()) {
                Log.e(TAG, "SDK清理失败!");
            } else {
                Log.i(TAG, "SDK清理成功!");
            }
        } catch (Exception e) {
            Log.e(TAG, "SDK清理异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取最后的错误码
     */
    public int getLastError() {
        return HCNetSDKJNAInstance.getInstance().NET_DVR_GetLastError();
    }
}
