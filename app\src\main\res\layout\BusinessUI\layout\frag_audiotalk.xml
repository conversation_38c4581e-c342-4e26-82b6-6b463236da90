<?xml version="1.0" encoding="utf-8"?>
<android.support.constraint.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/main_bg"
        android:gravity="center_vertical"
        android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_bytime_other"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/main_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <Button
            android:id="@+id/button_transmit"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="4"
            android:text="Transmit"
            android:textAllCaps="false"
            android:textSize="15sp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/button_intercom"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="4"
            android:text="Intercom"
            android:textAllCaps="false"
            android:textSize="15sp" />

        <Space
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/button_audiotalk_stop"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="4"
            android:text="Stop"
            android:textAllCaps="false"
            android:textSize="15sp" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="-146dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"/>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:baselineAligned="true"
            android:orientation="horizontal"/>
    </LinearLayout>
    </LinearLayout>


</android.support.constraint.ConstraintLayout>