package com.hik.camerasdk;

import android.util.Log;
import android.view.Surface;
import android.view.SurfaceHolder;
import com.hcnetsdk.jna.HCNetSDKByJNA;
import com.hcnetsdk.jna.HCNetSDKJNAInstance;
import java.util.ArrayList;
import java.util.List;

/**
 * 预览管理类
 * 负责摄像头预览功能
 */
public class PreviewManager {
    private static final String TAG = "PreviewManager";
    private static PreviewManager instance;
    private int currentPreviewHandle = -1;
    private CameraInfo currentCamera;
    
    private PreviewManager() {
    }
    
    public static synchronized PreviewManager getInstance() {
        if (instance == null) {
            instance = new PreviewManager();
        }
        return instance;
    }
    
    /**
     * 获取摄像头列表
     * 根据设备信息生成摄像头列表
     */
    public List<CameraInfo> getCameraList(DeviceInfo deviceInfo) {
        List<CameraInfo> cameraList = new ArrayList<>();
        
        if (deviceInfo == null || !deviceInfo.isOnline()) {
            Log.w(TAG, "设备未在线，无法获取摄像头列表");
            return cameraList;
        }
        
        try {
            // 获取设备信息来确定通道数
            // 这里简化处理，假设设备有4个通道
            // 实际应用中应该从设备信息结构体中获取通道数
            int channelCount = 4; // 可以从deviceInfo中获取实际通道数
            
            for (int i = 1; i <= channelCount; i++) {
                CameraInfo camera = new CameraInfo(i, "摄像头" + i);
                camera.setOnline(true);
                cameraList.add(camera);
            }
            
            Log.i(TAG, "获取到 " + cameraList.size() + " 个摄像头");
            
        } catch (Exception e) {
            Log.e(TAG, "获取摄像头列表异常: " + e.getMessage());
        }
        
        return cameraList;
    }
    
    /**
     * 开始预览
     * 使用SurfaceHolder进行预览
     */
    public boolean startPreview(DeviceInfo deviceInfo, CameraInfo cameraInfo, SurfaceHolder surfaceHolder) {
        try {
            if (deviceInfo == null || !deviceInfo.isOnline()) {
                Log.e(TAG, "设备未在线，无法开始预览");
                return false;
            }
            
            if (currentPreviewHandle >= 0) {
                Log.w(TAG, "已有预览在进行，先停止当前预览");
                stopPreview();
            }
            
            // 创建预览信息结构体
            HCNetSDKByJNA.NET_DVR_PREVIEWINFO previewInfo = new HCNetSDKByJNA.NET_DVR_PREVIEWINFO();
            previewInfo.lChannel = cameraInfo.getChannelNumber();
            previewInfo.dwStreamType = cameraInfo.getStreamType();
            previewInfo.bBlocked = 1;
            previewInfo.hHwnd = surfaceHolder;
            
            // 写入结构体
            previewInfo.write();
            
            // 调用预览接口
            currentPreviewHandle = HCNetSDKJNAInstance.getInstance().NET_DVR_RealPlay_V40(
                deviceInfo.getUserId(), previewInfo.getPointer(), null);
            
            if (currentPreviewHandle < 0) {
                int errorCode = SDKManager.getInstance().getLastError();
                Log.e(TAG, "开始预览失败! 错误码: " + errorCode);
                return false;
            }
            
            currentCamera = cameraInfo;
            Log.i(TAG, "预览开始成功! 句柄: " + currentPreviewHandle);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "开始预览异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 开始预览
     * 使用Surface进行预览（用于TextureView）
     */
    public boolean startPreview(DeviceInfo deviceInfo, CameraInfo cameraInfo, Surface surface) {
        try {
            if (deviceInfo == null || !deviceInfo.isOnline()) {
                Log.e(TAG, "设备未在线，无法开始预览");
                return false;
            }
            
            if (currentPreviewHandle >= 0) {
                Log.w(TAG, "已有预览在进行，先停止当前预览");
                stopPreview();
            }
            
            // 创建预览信息结构体
            HCNetSDKByJNA.NET_DVR_PREVIEWINFO_V20 previewInfo = new HCNetSDKByJNA.NET_DVR_PREVIEWINFO_V20();
            previewInfo.lChannel = cameraInfo.getChannelNumber();
            previewInfo.dwStreamType = cameraInfo.getStreamType();
            previewInfo.hHwnd = surface;
            
            // 写入结构体
            previewInfo.write();
            
            // 调用预览接口
            currentPreviewHandle = HCNetSDKJNAInstance.getInstance().NET_DVR_RealPlay_V40(
                deviceInfo.getUserId(), previewInfo.getPointer(), null);
            
            if (currentPreviewHandle < 0) {
                int errorCode = SDKManager.getInstance().getLastError();
                Log.e(TAG, "开始预览失败! 错误码: " + errorCode);
                return false;
            }
            
            currentCamera = cameraInfo;
            Log.i(TAG, "预览开始成功! 句柄: " + currentPreviewHandle);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "开始预览异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 停止预览
     */
    public boolean stopPreview() {
        try {
            if (currentPreviewHandle < 0) {
                Log.w(TAG, "没有正在进行的预览");
                return true;
            }
            
            boolean result = HCNetSDKJNAInstance.getInstance().NET_DVR_StopRealPlay(currentPreviewHandle);
            
            if (result) {
                Log.i(TAG, "预览停止成功");
            } else {
                int errorCode = SDKManager.getInstance().getLastError();
                Log.e(TAG, "预览停止失败! 错误码: " + errorCode);
            }
            
            currentPreviewHandle = -1;
            currentCamera = null;
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "停止预览异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 抓拍图片
     */
    public boolean capturePicture(String filePath) {
        try {
            if (currentPreviewHandle < 0) {
                Log.e(TAG, "没有正在进行的预览，无法抓拍");
                return false;
            }
            
            boolean result = HCNetSDKJNAInstance.getInstance().NET_DVR_CapturePicture(currentPreviewHandle, filePath);
            
            if (result) {
                Log.i(TAG, "抓拍成功，保存到: " + filePath);
            } else {
                int errorCode = SDKManager.getInstance().getLastError();
                Log.e(TAG, "抓拍失败! 错误码: " + errorCode);
            }
            
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "抓拍异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取当前预览的摄像头
     */
    public CameraInfo getCurrentCamera() {
        return currentCamera;
    }
    
    /**
     * 是否正在预览
     */
    public boolean isPreviewActive() {
        return currentPreviewHandle >= 0;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (currentPreviewHandle >= 0) {
            stopPreview();
        }
    }
}
