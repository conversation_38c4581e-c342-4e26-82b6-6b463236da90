<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/main_bg"
    android:orientation="vertical"
    tools:context=".View.BusinessUI.Fragment.PlayBack.FragPlayBackTheSelectFile">
    <SurfaceView
        android:id="@+id/Sur_Player"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:background="@color/select_title_text" />

    <android.support.v7.widget.AppCompatSeekBar
        android:id = "@+id/seekBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/Current_Progress"
        android:layout_marginRight="5dp"
        android:gravity="center_vertical" />


    <LinearLayout
        android:id="@+id/ll_play"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/main_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <Button
            android:id="@+id/button_play"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="50dp"
            android:textSize="15sp"
            android:textAllCaps="false"
            android:layout_marginLeft="25dp"
            android:text="@string/Forward_playback"/>

        <Space
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/button_reverse"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="50dp"
            android:textSize="15sp"
            android:textAllCaps="false"
            android:layout_marginRight="25dp"
            android:text="@string/Reverse_playback"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_other"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/main_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <Button
            android:id="@+id/button_stop"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="50dp"
            android:textSize="15sp"
            android:textAllCaps="false"
            android:layout_marginLeft="25dp"
            android:text="@string/Stop_playback"/>

        <Space
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/button_download"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:layout_height="50dp"
            android:textSize="15sp"
            android:textAllCaps="false"
            android:layout_marginRight="25dp"
            android:text="@string/Download_file"/>

    </LinearLayout>

</LinearLayout>


