<?xml version="1.0" encoding="utf-8"?>
<android.support.v4.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context=".View.MainActivity"
    tools:showIn="@layout/app_bar_main">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <android.support.design.widget.TabLayout
            android:id="@+id/fag_tabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="0dp"
            android:layout_marginTop="0dp"
            app:tabMode="scrollable"/>
        <android.support.v4.view.ViewPager
            android:id="@+id/fag_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>

</android.support.v4.widget.DrawerLayout>