package com.hik.camerasdk;

import android.util.Log;
import com.hcnetsdk.jna.HCNetSDKByJNA;
import com.hcnetsdk.jna.HCNetSDKJNAInstance;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备管理类
 * 负责设备的登录、登出和管理
 */
public class DeviceManager {
    private static final String TAG = "DeviceManager";
    private static DeviceManager instance;
    private List<DeviceInfo> deviceList;
    private DeviceInfo currentDevice;
    
    private DeviceManager() {
        deviceList = new ArrayList<>();
    }
    
    public static synchronized DeviceManager getInstance() {
        if (instance == null) {
            instance = new DeviceManager();
        }
        return instance;
    }
    
    /**
     * 登录设备
     */
    public boolean loginDevice(DeviceInfo deviceInfo) {
        try {
            // 创建登录信息结构体
            HCNetSDKByJNA.NET_DVR_USER_LOGIN_INFO loginInfo = new HCNetSDKByJNA.NET_DVR_USER_LOGIN_INFO();
            
            // 设置登录参数
            System.arraycopy(deviceInfo.getIpAddress().getBytes(), 0, 
                           loginInfo.sDeviceAddress, 0, deviceInfo.getIpAddress().length());
            System.arraycopy(deviceInfo.getUsername().getBytes(), 0, 
                           loginInfo.sUserName, 0, deviceInfo.getUsername().length());
            System.arraycopy(deviceInfo.getPassword().getBytes(), 0, 
                           loginInfo.sPassword, 0, deviceInfo.getPassword().length());
            loginInfo.wPort = (short) deviceInfo.getPort();
            
            // 创建设备信息结构体
            HCNetSDKByJNA.NET_DVR_DEVICEINFO_V40 deviceInfoV40 = new HCNetSDKByJNA.NET_DVR_DEVICEINFO_V40();
            
            // 写入结构体
            loginInfo.write();
            
            // 调用登录接口
            int userId = HCNetSDKJNAInstance.getInstance().NET_DVR_Login_V40(
                loginInfo.getPointer(), deviceInfoV40.getPointer());
            
            if (userId < 0) {
                int errorCode = SDKManager.getInstance().getLastError();
                Log.e(TAG, "设备登录失败! 错误码: " + errorCode);
                return false;
            }
            
            // 登录成功，更新设备信息
            deviceInfo.setUserId(userId);
            deviceInfo.setOnline(true);
            currentDevice = deviceInfo;
            
            // 添加到设备列表（如果不存在）
            if (!deviceList.contains(deviceInfo)) {
                deviceList.add(deviceInfo);
            }
            
            Log.i(TAG, "设备登录成功! 用户ID: " + userId);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "设备登录异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 登出设备
     */
    public boolean logoutDevice(DeviceInfo deviceInfo) {
        try {
            if (deviceInfo.getUserId() < 0) {
                Log.w(TAG, "设备未登录，无需登出");
                return true;
            }
            
            boolean result = HCNetSDKJNAInstance.getInstance().NET_DVR_Logout(deviceInfo.getUserId());
            
            if (result) {
                deviceInfo.setUserId(-1);
                deviceInfo.setOnline(false);
                if (currentDevice == deviceInfo) {
                    currentDevice = null;
                }
                Log.i(TAG, "设备登出成功");
            } else {
                int errorCode = SDKManager.getInstance().getLastError();
                Log.e(TAG, "设备登出失败! 错误码: " + errorCode);
            }
            
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "设备登出异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取当前设备
     */
    public DeviceInfo getCurrentDevice() {
        return currentDevice;
    }
    
    /**
     * 获取设备列表
     */
    public List<DeviceInfo> getDeviceList() {
        return new ArrayList<>(deviceList);
    }
    
    /**
     * 清理所有设备连接
     */
    public void cleanup() {
        for (DeviceInfo device : deviceList) {
            if (device.isOnline()) {
                logoutDevice(device);
            }
        }
        deviceList.clear();
        currentDevice = null;
    }
}
